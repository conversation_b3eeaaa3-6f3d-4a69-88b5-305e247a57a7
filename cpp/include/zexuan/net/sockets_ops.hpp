#ifndef ZEXUAN_NET_SOCKETS_OPS_HPP
#define ZEXUAN_NET_SOCKETS_OPS_HPP

#include <cstddef>
#include <cstdint>

// 平台特定的头文件包含
#ifdef __linux__
#  include <arpa/inet.h>
#  include <endian.h>
#  include <netinet/in.h>
#  include <sys/socket.h>
#  include <sys/uio.h>
#  include <unistd.h>
#else
#  include <winsock2.h>
#  include <ws2tcpip.h>
#endif

namespace zexuan {
  namespace net {
    namespace sockets {

        // 套接字创建和基本操作
        int createNonblockingOrDie(sa_family_t family);
        int connect(int sockfd, const struct sockaddr* addr);
        void bindOrDie(int sockfd, const struct sockaddr* addr);
        void listenOrDie(int sockfd);
        int accept(int sockfd, struct sockaddr_in6* addr);

        // 数据读写操作
        ssize_t read(int sockfd, void* buf, size_t count);
        ssize_t readv(int sockfd, const struct iovec* iov, int iovcnt);
        ssize_t write(int sockfd, const void* buf, size_t count);

        // 套接字关闭操作
        void close(int sockfd);
        void shutdownWrite(int sockfd);

        // 地址转换函数
        void toIpPort(char* buf, size_t size, const struct sockaddr* addr);
        void toIp(char* buf, size_t size, const struct sockaddr* addr);

        // IP地址和端口设置函数
        void fromIpPort(const char* ip, uint16_t port, struct sockaddr_in* addr);
        void fromIpPort(const char* ip, uint16_t port, struct sockaddr_in6* addr);

        // 套接字错误处理
        int getSocketError(int sockfd);

        // 地址类型转换函数
        const struct sockaddr* sockaddr_cast(const struct sockaddr_in* addr);
        const struct sockaddr* sockaddr_cast(const struct sockaddr_in6* addr);
        struct sockaddr* sockaddr_cast(struct sockaddr_in6* addr);
        const struct sockaddr_in* sockaddr_in_cast(const struct sockaddr* addr);
        const struct sockaddr_in6* sockaddr_in6_cast(const struct sockaddr* addr);

        // 地址获取函数
        struct sockaddr_in6 getLocalAddr(int sockfd);
        struct sockaddr_in6 getPeerAddr(int sockfd);
        bool isSelfConnect(int sockfd);

    }  // namespace sockets
  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_SOCKETS_OPS_HPP
