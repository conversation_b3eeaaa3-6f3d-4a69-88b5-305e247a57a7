=================================================================
==58994==ERROR: LeakSanitizer: detected memory leaks

Indirect leak of 392 byte(s) in 1 object(s) allocated from:
    #0 0x6325352488cd in operator new(unsigned long) (/root/zexuan/cpp/build/bin/zexuan+0x1828cd) (BuildId: 69aafd08042e9a9fce7d41b8fcdafb1340de610e)
    #1 0x7ca0388b138c in std::__shared_count<(__gnu_cxx::_Lock_policy)2>::__shared_count<zexuan::base::Observer, std::allocator<void>, int const&, std::shared_ptr<zexuan::base::Mediator>&>(zexuan::base::Observer*&, std::_Sp_alloc_shared_tag<std::allocator<void>>, int const&, std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xd638c) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #2 0x7ca0388eefe5 in zexuan::protocol::service::ProtocolService::ProtocolService(std::shared_ptr<zexuan::base::Mediator>) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x113fe5) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #3 0x7ca03886c789 in std::__detail::_MakeUniq<zexuan::protocol::service::ProtocolService>::__single_object std::make_unique<zexuan::protocol::service::ProtocolService, std::shared_ptr<zexuan::base::Mediator>&>(std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x91789) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #4 0x7ca038855acb in zexuan::protocol::gateway::ProtocolGateway::CreateProtocolService() (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x7aacb) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #5 0x7ca03885440b in zexuan::protocol::gateway::ProtocolGateway::ProtocolGateway(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, std::shared_ptr<zexuan::base::Mediator>) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x7940b) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #6 0x7ca0388eaa09 in void std::_Construct<zexuan::protocol::gateway::ProtocolGateway, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&>(zexuan::protocol::gateway::ProtocolGateway*, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x10fa09) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #7 0x7ca0388ea1d2 in std::__shared_count<(__gnu_cxx::_Lock_policy)2>::__shared_count<zexuan::protocol::gateway::ProtocolGateway, std::allocator<void>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&>(zexuan::protocol::gateway::ProtocolGateway*&, std::_Sp_alloc_shared_tag<std::allocator<void>>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x10f1d2) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #8 0x7ca0388d768f in zexuan::protocol::server::ProtocolServer::CreateGatewayForConnection(std::shared_ptr<zexuan::net::TcpConnection> const&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xfc68f) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #9 0x7ca0388d6d61 in zexuan::protocol::server::ProtocolServer::OnConnection(std::shared_ptr<zexuan::net::TcpConnection> const&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xfbd61) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #10 0x7ca038740e07 in zexuan::net::TcpConnection::connectEstablished() (/root/zexuan/cpp/build/lib/libcore.so.1+0x118e07) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #11 0x7ca03871ccac in zexuan::net::EventLoop::doPendingFunctors() (/root/zexuan/cpp/build/lib/libcore.so.1+0xf4cac) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #12 0x7ca03871c669 in zexuan::net::EventLoop::loop() (/root/zexuan/cpp/build/lib/libcore.so.1+0xf4669) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #13 0x7ca038722081 in zexuan::net::EventLoopThread::threadFunc() (/root/zexuan/cpp/build/lib/libcore.so.1+0xfa081) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #14 0x7ca0380e55a3 in execute_native_thread_routine /usr/src/debug/gcc/gcc/libstdc++-v3/src/c++11/thread.cc:104:18
    #15 0x63253512264c in asan_thread_start(void*) asan_interceptors.cpp.o

Indirect leak of 344 byte(s) in 1 object(s) allocated from:
    #0 0x6325352488cd in operator new(unsigned long) (/root/zexuan/cpp/build/bin/zexuan+0x1828cd) (BuildId: 69aafd08042e9a9fce7d41b8fcdafb1340de610e)
    #1 0x7ca0388e9841 in std::__shared_count<(__gnu_cxx::_Lock_policy)2>::__shared_count<zexuan::base::Mediator, std::allocator<void>>(zexuan::base::Mediator*&, std::_Sp_alloc_shared_tag<std::allocator<void>>) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x10e841) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #2 0x7ca0388d761b in zexuan::protocol::server::ProtocolServer::CreateGatewayForConnection(std::shared_ptr<zexuan::net::TcpConnection> const&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xfc61b) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #3 0x7ca0388d6d61 in zexuan::protocol::server::ProtocolServer::OnConnection(std::shared_ptr<zexuan::net::TcpConnection> const&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xfbd61) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #4 0x7ca038740e07 in zexuan::net::TcpConnection::connectEstablished() (/root/zexuan/cpp/build/lib/libcore.so.1+0x118e07) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #5 0x7ca03871ccac in zexuan::net::EventLoop::doPendingFunctors() (/root/zexuan/cpp/build/lib/libcore.so.1+0xf4cac) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #6 0x7ca03871c669 in zexuan::net::EventLoop::loop() (/root/zexuan/cpp/build/lib/libcore.so.1+0xf4669) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #7 0x7ca038722081 in zexuan::net::EventLoopThread::threadFunc() (/root/zexuan/cpp/build/lib/libcore.so.1+0xfa081) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #8 0x7ca0380e55a3 in execute_native_thread_routine /usr/src/debug/gcc/gcc/libstdc++-v3/src/c++11/thread.cc:104:18
    #9 0x63253512264c in asan_thread_start(void*) asan_interceptors.cpp.o

Indirect leak of 344 byte(s) in 1 object(s) allocated from:
    #0 0x6325352488cd in operator new(unsigned long) (/root/zexuan/cpp/build/bin/zexuan+0x1828cd) (BuildId: 69aafd08042e9a9fce7d41b8fcdafb1340de610e)
    #1 0x7ca0388b2c0c in std::__shared_count<(__gnu_cxx::_Lock_policy)2>::__shared_count<zexuan::base::Subject, std::allocator<void>, int const&, std::shared_ptr<zexuan::base::Mediator>&>(zexuan::base::Subject*&, std::_Sp_alloc_shared_tag<std::allocator<void>>, int const&, std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xd7c0c) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #2 0x7ca0388ef304 in zexuan::protocol::service::ProtocolService::ProtocolService(std::shared_ptr<zexuan::base::Mediator>) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x114304) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #3 0x7ca03886c789 in std::__detail::_MakeUniq<zexuan::protocol::service::ProtocolService>::__single_object std::make_unique<zexuan::protocol::service::ProtocolService, std::shared_ptr<zexuan::base::Mediator>&>(std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x91789) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #4 0x7ca038855acb in zexuan::protocol::gateway::ProtocolGateway::CreateProtocolService() (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x7aacb) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #5 0x7ca03885440b in zexuan::protocol::gateway::ProtocolGateway::ProtocolGateway(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, std::shared_ptr<zexuan::base::Mediator>) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x7940b) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #6 0x7ca0388eaa09 in void std::_Construct<zexuan::protocol::gateway::ProtocolGateway, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&>(zexuan::protocol::gateway::ProtocolGateway*, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x10fa09) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #7 0x7ca0388ea1d2 in std::__shared_count<(__gnu_cxx::_Lock_policy)2>::__shared_count<zexuan::protocol::gateway::ProtocolGateway, std::allocator<void>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&>(zexuan::protocol::gateway::ProtocolGateway*&, std::_Sp_alloc_shared_tag<std::allocator<void>>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x10f1d2) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #8 0x7ca0388d768f in zexuan::protocol::server::ProtocolServer::CreateGatewayForConnection(std::shared_ptr<zexuan::net::TcpConnection> const&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xfc68f) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #9 0x7ca0388d6d61 in zexuan::protocol::server::ProtocolServer::OnConnection(std::shared_ptr<zexuan::net::TcpConnection> const&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xfbd61) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #10 0x7ca038740e07 in zexuan::net::TcpConnection::connectEstablished() (/root/zexuan/cpp/build/lib/libcore.so.1+0x118e07) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #11 0x7ca03871ccac in zexuan::net::EventLoop::doPendingFunctors() (/root/zexuan/cpp/build/lib/libcore.so.1+0xf4cac) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #12 0x7ca03871c669 in zexuan::net::EventLoop::loop() (/root/zexuan/cpp/build/lib/libcore.so.1+0xf4669) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #13 0x7ca038722081 in zexuan::net::EventLoopThread::threadFunc() (/root/zexuan/cpp/build/lib/libcore.so.1+0xfa081) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #14 0x7ca0380e55a3 in execute_native_thread_routine /usr/src/debug/gcc/gcc/libstdc++-v3/src/c++11/thread.cc:104:18
    #15 0x63253512264c in asan_thread_start(void*) asan_interceptors.cpp.o

Indirect leak of 104 byte(s) in 1 object(s) allocated from:
    #0 0x6325352488cd in operator new(unsigned long) (/root/zexuan/cpp/build/bin/zexuan+0x1828cd) (BuildId: 69aafd08042e9a9fce7d41b8fcdafb1340de610e)
    #1 0x7ca0386c0ec8 in std::_Hashtable<int, std::pair<int const, std::shared_ptr<zexuan::base::Observer>>, std::allocator<std::pair<int const, std::shared_ptr<zexuan::base::Observer>>>, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>>::_M_rehash(unsigned long, std::integral_constant<bool, true>) (/root/zexuan/cpp/build/lib/libcore.so.1+0x98ec8) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #2 0x7ca0386c0977 in std::_Hashtable<int, std::pair<int const, std::shared_ptr<zexuan::base::Observer>>, std::allocator<std::pair<int const, std::shared_ptr<zexuan::base::Observer>>>, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>>::_M_insert_unique_node(unsigned long, unsigned long, std::__detail::_Hash_node<std::pair<int const, std::shared_ptr<zexuan::base::Observer>>, false>*, unsigned long) (/root/zexuan/cpp/build/lib/libcore.so.1+0x98977) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #3 0x7ca0386c04af in std::__detail::_Map_base<int, std::pair<int const, std::shared_ptr<zexuan::base::Observer>>, std::allocator<std::pair<int const, std::shared_ptr<zexuan::base::Observer>>>, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>, true>::operator[](int const&) (/root/zexuan/cpp/build/lib/libcore.so.1+0x984af) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #4 0x7ca0386b8d27 in zexuan::base::Mediator::EnrollObserver(int, std::shared_ptr<zexuan::base::Observer>) (/root/zexuan/cpp/build/lib/libcore.so.1+0x90d27) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #5 0x7ca038701ee9 in zexuan::base::Observer::DoRegister() (/root/zexuan/cpp/build/lib/libcore.so.1+0xd9ee9) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #6 0x7ca0387065f3 in zexuan::base::RegisterObject::RegisterToMediator() (/root/zexuan/cpp/build/lib/libcore.so.1+0xde5f3) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #7 0x7ca038705375 in zexuan::base::RegisterObject::Init() (/root/zexuan/cpp/build/lib/libcore.so.1+0xdd375) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #8 0x7ca0388f07b2 in zexuan::protocol::service::ProtocolService::Initialize() (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x1157b2) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #9 0x7ca038855e8b in zexuan::protocol::gateway::ProtocolGateway::CreateProtocolService() (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x7ae8b) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #10 0x7ca03885440b in zexuan::protocol::gateway::ProtocolGateway::ProtocolGateway(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, std::shared_ptr<zexuan::base::Mediator>) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x7940b) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #11 0x7ca0388eaa09 in void std::_Construct<zexuan::protocol::gateway::ProtocolGateway, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&>(zexuan::protocol::gateway::ProtocolGateway*, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x10fa09) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #12 0x7ca0388ea1d2 in std::__shared_count<(__gnu_cxx::_Lock_policy)2>::__shared_count<zexuan::protocol::gateway::ProtocolGateway, std::allocator<void>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&>(zexuan::protocol::gateway::ProtocolGateway*&, std::_Sp_alloc_shared_tag<std::allocator<void>>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x10f1d2) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #13 0x7ca0388d768f in zexuan::protocol::server::ProtocolServer::CreateGatewayForConnection(std::shared_ptr<zexuan::net::TcpConnection> const&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xfc68f) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #14 0x7ca0388d6d61 in zexuan::protocol::server::ProtocolServer::OnConnection(std::shared_ptr<zexuan::net::TcpConnection> const&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xfbd61) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #15 0x7ca038740e07 in zexuan::net::TcpConnection::connectEstablished() (/root/zexuan/cpp/build/lib/libcore.so.1+0x118e07) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #16 0x7ca03871ccac in zexuan::net::EventLoop::doPendingFunctors() (/root/zexuan/cpp/build/lib/libcore.so.1+0xf4cac) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #17 0x7ca03871c669 in zexuan::net::EventLoop::loop() (/root/zexuan/cpp/build/lib/libcore.so.1+0xf4669) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #18 0x7ca038722081 in zexuan::net::EventLoopThread::threadFunc() (/root/zexuan/cpp/build/lib/libcore.so.1+0xfa081) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #19 0x7ca0380e55a3 in execute_native_thread_routine /usr/src/debug/gcc/gcc/libstdc++-v3/src/c++11/thread.cc:104:18
    #20 0x63253512264c in asan_thread_start(void*) asan_interceptors.cpp.o

Indirect leak of 104 byte(s) in 1 object(s) allocated from:
    #0 0x6325352488cd in operator new(unsigned long) (/root/zexuan/cpp/build/bin/zexuan+0x1828cd) (BuildId: 69aafd08042e9a9fce7d41b8fcdafb1340de610e)
    #1 0x7ca0386f4f08 in std::_Hashtable<int, std::pair<int const, std::shared_ptr<zexuan::base::Subject>>, std::allocator<std::pair<int const, std::shared_ptr<zexuan::base::Subject>>>, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>>::_M_rehash(unsigned long, std::integral_constant<bool, true>) (/root/zexuan/cpp/build/lib/libcore.so.1+0xccf08) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #2 0x7ca0386f49b7 in std::_Hashtable<int, std::pair<int const, std::shared_ptr<zexuan::base::Subject>>, std::allocator<std::pair<int const, std::shared_ptr<zexuan::base::Subject>>>, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>>::_M_insert_unique_node(unsigned long, unsigned long, std::__detail::_Hash_node<std::pair<int const, std::shared_ptr<zexuan::base::Subject>>, false>*, unsigned long) (/root/zexuan/cpp/build/lib/libcore.so.1+0xcc9b7) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #3 0x7ca0386f44ef in std::__detail::_Map_base<int, std::pair<int const, std::shared_ptr<zexuan::base::Subject>>, std::allocator<std::pair<int const, std::shared_ptr<zexuan::base::Subject>>>, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>, true>::operator[](int const&) (/root/zexuan/cpp/build/lib/libcore.so.1+0xcc4ef) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #4 0x7ca0386b9a70 in zexuan::base::Mediator::EnrollSubject(int, std::shared_ptr<zexuan::base::Subject>) (/root/zexuan/cpp/build/lib/libcore.so.1+0x91a70) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #5 0x7ca03870a6b9 in zexuan::base::Subject::DoRegister() (/root/zexuan/cpp/build/lib/libcore.so.1+0xe26b9) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #6 0x7ca0387065f3 in zexuan::base::RegisterObject::RegisterToMediator() (/root/zexuan/cpp/build/lib/libcore.so.1+0xde5f3) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #7 0x7ca038705375 in zexuan::base::RegisterObject::Init() (/root/zexuan/cpp/build/lib/libcore.so.1+0xdd375) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #8 0x7ca0388f08a5 in zexuan::protocol::service::ProtocolService::Initialize() (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x1158a5) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #9 0x7ca038855e8b in zexuan::protocol::gateway::ProtocolGateway::CreateProtocolService() (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x7ae8b) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #10 0x7ca03885440b in zexuan::protocol::gateway::ProtocolGateway::ProtocolGateway(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, std::shared_ptr<zexuan::base::Mediator>) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x7940b) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #11 0x7ca0388eaa09 in void std::_Construct<zexuan::protocol::gateway::ProtocolGateway, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&>(zexuan::protocol::gateway::ProtocolGateway*, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x10fa09) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #12 0x7ca0388ea1d2 in std::__shared_count<(__gnu_cxx::_Lock_policy)2>::__shared_count<zexuan::protocol::gateway::ProtocolGateway, std::allocator<void>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&>(zexuan::protocol::gateway::ProtocolGateway*&, std::_Sp_alloc_shared_tag<std::allocator<void>>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x10f1d2) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #13 0x7ca0388d768f in zexuan::protocol::server::ProtocolServer::CreateGatewayForConnection(std::shared_ptr<zexuan::net::TcpConnection> const&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xfc68f) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #14 0x7ca0388d6d61 in zexuan::protocol::server::ProtocolServer::OnConnection(std::shared_ptr<zexuan::net::TcpConnection> const&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xfbd61) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #15 0x7ca038740e07 in zexuan::net::TcpConnection::connectEstablished() (/root/zexuan/cpp/build/lib/libcore.so.1+0x118e07) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #16 0x7ca03871ccac in zexuan::net::EventLoop::doPendingFunctors() (/root/zexuan/cpp/build/lib/libcore.so.1+0xf4cac) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #17 0x7ca03871c669 in zexuan::net::EventLoop::loop() (/root/zexuan/cpp/build/lib/libcore.so.1+0xf4669) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #18 0x7ca038722081 in zexuan::net::EventLoopThread::threadFunc() (/root/zexuan/cpp/build/lib/libcore.so.1+0xfa081) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #19 0x7ca0380e55a3 in execute_native_thread_routine /usr/src/debug/gcc/gcc/libstdc++-v3/src/c++11/thread.cc:104:18
    #20 0x63253512264c in asan_thread_start(void*) asan_interceptors.cpp.o

Indirect leak of 32 byte(s) in 1 object(s) allocated from:
    #0 0x6325352488cd in operator new(unsigned long) (/root/zexuan/cpp/build/bin/zexuan+0x1828cd) (BuildId: 69aafd08042e9a9fce7d41b8fcdafb1340de610e)
    #1 0x7ca0386f4d16 in std::__detail::_Hash_node<std::pair<int const, std::shared_ptr<zexuan::base::Subject>>, false>* std::__detail::_Hashtable_alloc<std::allocator<std::__detail::_Hash_node<std::pair<int const, std::shared_ptr<zexuan::base::Subject>>, false>>>::_M_allocate_node<std::piecewise_construct_t const&, std::tuple<int const&>, std::tuple<>>(std::piecewise_construct_t const&, std::tuple<int const&>&&, std::tuple<>&&) (/root/zexuan/cpp/build/lib/libcore.so.1+0xccd16) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #2 0x7ca0386f44b5 in std::__detail::_Map_base<int, std::pair<int const, std::shared_ptr<zexuan::base::Subject>>, std::allocator<std::pair<int const, std::shared_ptr<zexuan::base::Subject>>>, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>, true>::operator[](int const&) (/root/zexuan/cpp/build/lib/libcore.so.1+0xcc4b5) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #3 0x7ca0386b9a70 in zexuan::base::Mediator::EnrollSubject(int, std::shared_ptr<zexuan::base::Subject>) (/root/zexuan/cpp/build/lib/libcore.so.1+0x91a70) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #4 0x7ca03870a6b9 in zexuan::base::Subject::DoRegister() (/root/zexuan/cpp/build/lib/libcore.so.1+0xe26b9) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #5 0x7ca0387065f3 in zexuan::base::RegisterObject::RegisterToMediator() (/root/zexuan/cpp/build/lib/libcore.so.1+0xde5f3) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #6 0x7ca038705375 in zexuan::base::RegisterObject::Init() (/root/zexuan/cpp/build/lib/libcore.so.1+0xdd375) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #7 0x7ca0388f08a5 in zexuan::protocol::service::ProtocolService::Initialize() (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x1158a5) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #8 0x7ca038855e8b in zexuan::protocol::gateway::ProtocolGateway::CreateProtocolService() (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x7ae8b) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #9 0x7ca03885440b in zexuan::protocol::gateway::ProtocolGateway::ProtocolGateway(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, std::shared_ptr<zexuan::base::Mediator>) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x7940b) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #10 0x7ca0388eaa09 in void std::_Construct<zexuan::protocol::gateway::ProtocolGateway, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&>(zexuan::protocol::gateway::ProtocolGateway*, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x10fa09) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #11 0x7ca0388ea1d2 in std::__shared_count<(__gnu_cxx::_Lock_policy)2>::__shared_count<zexuan::protocol::gateway::ProtocolGateway, std::allocator<void>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&>(zexuan::protocol::gateway::ProtocolGateway*&, std::_Sp_alloc_shared_tag<std::allocator<void>>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x10f1d2) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #12 0x7ca0388d768f in zexuan::protocol::server::ProtocolServer::CreateGatewayForConnection(std::shared_ptr<zexuan::net::TcpConnection> const&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xfc68f) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #13 0x7ca0388d6d61 in zexuan::protocol::server::ProtocolServer::OnConnection(std::shared_ptr<zexuan::net::TcpConnection> const&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xfbd61) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #14 0x7ca038740e07 in zexuan::net::TcpConnection::connectEstablished() (/root/zexuan/cpp/build/lib/libcore.so.1+0x118e07) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #15 0x7ca03871ccac in zexuan::net::EventLoop::doPendingFunctors() (/root/zexuan/cpp/build/lib/libcore.so.1+0xf4cac) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #16 0x7ca03871c669 in zexuan::net::EventLoop::loop() (/root/zexuan/cpp/build/lib/libcore.so.1+0xf4669) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #17 0x7ca038722081 in zexuan::net::EventLoopThread::threadFunc() (/root/zexuan/cpp/build/lib/libcore.so.1+0xfa081) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #18 0x7ca0380e55a3 in execute_native_thread_routine /usr/src/debug/gcc/gcc/libstdc++-v3/src/c++11/thread.cc:104:18
    #19 0x63253512264c in asan_thread_start(void*) asan_interceptors.cpp.o

Indirect leak of 32 byte(s) in 1 object(s) allocated from:
    #0 0x6325352488cd in operator new(unsigned long) (/root/zexuan/cpp/build/bin/zexuan+0x1828cd) (BuildId: 69aafd08042e9a9fce7d41b8fcdafb1340de610e)
    #1 0x7ca0386c0cd6 in std::__detail::_Hash_node<std::pair<int const, std::shared_ptr<zexuan::base::Observer>>, false>* std::__detail::_Hashtable_alloc<std::allocator<std::__detail::_Hash_node<std::pair<int const, std::shared_ptr<zexuan::base::Observer>>, false>>>::_M_allocate_node<std::piecewise_construct_t const&, std::tuple<int const&>, std::tuple<>>(std::piecewise_construct_t const&, std::tuple<int const&>&&, std::tuple<>&&) (/root/zexuan/cpp/build/lib/libcore.so.1+0x98cd6) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #2 0x7ca0386c0475 in std::__detail::_Map_base<int, std::pair<int const, std::shared_ptr<zexuan::base::Observer>>, std::allocator<std::pair<int const, std::shared_ptr<zexuan::base::Observer>>>, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>, true>::operator[](int const&) (/root/zexuan/cpp/build/lib/libcore.so.1+0x98475) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #3 0x7ca0386b8d27 in zexuan::base::Mediator::EnrollObserver(int, std::shared_ptr<zexuan::base::Observer>) (/root/zexuan/cpp/build/lib/libcore.so.1+0x90d27) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #4 0x7ca038701ee9 in zexuan::base::Observer::DoRegister() (/root/zexuan/cpp/build/lib/libcore.so.1+0xd9ee9) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #5 0x7ca0387065f3 in zexuan::base::RegisterObject::RegisterToMediator() (/root/zexuan/cpp/build/lib/libcore.so.1+0xde5f3) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #6 0x7ca038705375 in zexuan::base::RegisterObject::Init() (/root/zexuan/cpp/build/lib/libcore.so.1+0xdd375) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #7 0x7ca0388f07b2 in zexuan::protocol::service::ProtocolService::Initialize() (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x1157b2) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #8 0x7ca038855e8b in zexuan::protocol::gateway::ProtocolGateway::CreateProtocolService() (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x7ae8b) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #9 0x7ca03885440b in zexuan::protocol::gateway::ProtocolGateway::ProtocolGateway(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, std::shared_ptr<zexuan::base::Mediator>) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x7940b) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #10 0x7ca0388eaa09 in void std::_Construct<zexuan::protocol::gateway::ProtocolGateway, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&>(zexuan::protocol::gateway::ProtocolGateway*, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x10fa09) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #11 0x7ca0388ea1d2 in std::__shared_count<(__gnu_cxx::_Lock_policy)2>::__shared_count<zexuan::protocol::gateway::ProtocolGateway, std::allocator<void>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&>(zexuan::protocol::gateway::ProtocolGateway*&, std::_Sp_alloc_shared_tag<std::allocator<void>>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>>&, std::shared_ptr<zexuan::base::Mediator>&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0x10f1d2) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #12 0x7ca0388d768f in zexuan::protocol::server::ProtocolServer::CreateGatewayForConnection(std::shared_ptr<zexuan::net::TcpConnection> const&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xfc68f) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #13 0x7ca0388d6d61 in zexuan::protocol::server::ProtocolServer::OnConnection(std::shared_ptr<zexuan::net::TcpConnection> const&) (/root/zexuan/cpp/build/lib/libprotocol.so.1+0xfbd61) (BuildId: a92ad0c671d37464dd328fd253fcf8ccca971d85)
    #14 0x7ca038740e07 in zexuan::net::TcpConnection::connectEstablished() (/root/zexuan/cpp/build/lib/libcore.so.1+0x118e07) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #15 0x7ca03871ccac in zexuan::net::EventLoop::doPendingFunctors() (/root/zexuan/cpp/build/lib/libcore.so.1+0xf4cac) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #16 0x7ca03871c669 in zexuan::net::EventLoop::loop() (/root/zexuan/cpp/build/lib/libcore.so.1+0xf4669) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #17 0x7ca038722081 in zexuan::net::EventLoopThread::threadFunc() (/root/zexuan/cpp/build/lib/libcore.so.1+0xfa081) (BuildId: 991903ec13acaf633082189866e9ad2122b3f195)
    #18 0x7ca0380e55a3 in execute_native_thread_routine /usr/src/debug/gcc/gcc/libstdc++-v3/src/c++11/thread.cc:104:18
    #19 0x63253512264c in asan_thread_start(void*) asan_interceptors.cpp.o

SUMMARY: AddressSanitizer: 1352 byte(s) leaked in 7 allocation(s).